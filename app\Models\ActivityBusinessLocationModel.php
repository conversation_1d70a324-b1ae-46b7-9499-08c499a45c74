<?php

namespace App\Models;

use CodeIgniter\Model;

class ActivityBusinessLocationModel extends Model
{
    protected $table = 'activity_business_locations';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    
    protected $allowedFields = [
        'org_id',
        'activity_id',
        'business_location_id',
        'assigned_at'
    ];
    
    protected $useTimestamps = false;
    
    protected $validationRules = [
        'org_id' => 'required|integer',
        'activity_id' => 'required|integer',
        'business_location_id' => 'required|integer',
        'assigned_at' => 'permit_empty|valid_date'
    ];
    
    protected $validationMessages = [
        'org_id' => [
            'required' => 'Organization is required',
            'integer' => 'Invalid organization selection'
        ],
        'activity_id' => [
            'required' => 'Activity is required',
            'integer' => 'Invalid activity selection'
        ],
        'business_location_id' => [
            'required' => 'Business location is required',
            'integer' => 'Invalid business location selection'
        ],
        'assigned_at' => [
            'valid_date' => 'Invalid assignment date format'
        ]
    ];
    
    protected $skipValidation = false;
    
    /**
     * Custom validation to check unique assignment
     */
    protected $beforeInsert = ['validateUniqueAssignment'];
    
    protected function validateUniqueAssignment(array $data)
    {
        if (isset($data['data']['activity_id']) && isset($data['data']['business_location_id'])) {
            $existing = $this->where('activity_id', $data['data']['activity_id'])
                           ->where('business_location_id', $data['data']['business_location_id'])
                           ->first();
            
            if ($existing) {
                throw new \CodeIgniter\Database\Exceptions\DatabaseException('Business location is already assigned to this activity');
            }
        }
        
        return $data;
    }
    
    /**
     * Get business locations assigned to an activity
     */
    public function getLocationsByActivity(int $activityId)
    {
        return $this->select('activity_business_locations.*, business_locations.business_name, business_locations.gps_coordinates, business_locations.status as location_status')
                   ->join('business_locations', 'business_locations.id = activity_business_locations.business_location_id', 'left')
                   ->where('activity_business_locations.activity_id', $activityId)
                   ->where('business_locations.is_deleted', false)
                   ->orderBy('activity_business_locations.assigned_at', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get activities assigned to a business location
     */
    public function getActivitiesByLocation(int $businessLocationId)
    {
        return $this->select('activity_business_locations.*, activities.activity_name, activities.activity_type, activities.date_from, activities.date_to, activities.status')
                   ->join('activities', 'activities.id = activity_business_locations.activity_id', 'left')
                   ->where('activity_business_locations.business_location_id', $businessLocationId)
                   ->where('activities.is_deleted', false)
                   ->orderBy('activities.date_from', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get activities assigned to a business location by organization
     */
    public function getActivitiesByLocationAndOrg(int $businessLocationId, int $orgId)
    {
        return $this->select('activity_business_locations.*, activities.activity_name, activities.activity_type, activities.date_from, activities.date_to, activities.status')
                   ->join('activities', 'activities.id = activity_business_locations.activity_id', 'left')
                   ->where('activity_business_locations.business_location_id', $businessLocationId)
                   ->where('activity_business_locations.org_id', $orgId)
                   ->where('activities.is_deleted', false)
                   ->orderBy('activities.date_from', 'DESC')
                   ->findAll();
    }
    
    /**
     * Assign business location to activity
     */
    public function assignLocationToActivity(int $orgId, int $activityId, int $businessLocationId)
    {
        $data = [
            'org_id' => $orgId,
            'activity_id' => $activityId,
            'business_location_id' => $businessLocationId,
            'assigned_at' => date('Y-m-d H:i:s')
        ];
        
        return $this->insert($data);
    }
    
    /**
     * Assign multiple business locations to activity
     */
    public function assignLocationsToActivity(int $orgId, int $activityId, array $businessLocationIds)
    {
        $data = [];
        $assignedAt = date('Y-m-d H:i:s');
        
        foreach ($businessLocationIds as $businessLocationId) {
            $data[] = [
                'org_id' => $orgId,
                'activity_id' => $activityId,
                'business_location_id' => $businessLocationId,
                'assigned_at' => $assignedAt
            ];
        }
        
        return $this->insertBatch($data);
    }
    
    /**
     * Remove business location from activity
     */
    public function removeLocationFromActivity(int $activityId, int $businessLocationId)
    {
        return $this->where('activity_id', $activityId)
                   ->where('business_location_id', $businessLocationId)
                   ->delete();
    }
    
    /**
     * Remove all business locations from activity
     */
    public function removeAllLocationsFromActivity(int $activityId)
    {
        return $this->where('activity_id', $activityId)
                   ->delete();
    }
    
    /**
     * Check if business location is assigned to activity
     */
    public function isLocationAssignedToActivity(int $activityId, int $businessLocationId)
    {
        $assignment = $this->where('activity_id', $activityId)
                          ->where('business_location_id', $businessLocationId)
                          ->first();
        
        return !empty($assignment);
    }
    
    /**
     * Get assignment statistics by activity
     */
    public function getAssignmentStatsByActivity(int $activityId)
    {
        return $this->where('activity_id', $activityId)
                   ->countAllResults();
    }
    
    /**
     * Get assignment statistics by business location
     */
    public function getAssignmentStatsByLocation(int $businessLocationId, int $orgId = null)
    {
        $builder = $this->where('business_location_id', $businessLocationId);
        
        if ($orgId) {
            $builder->where('org_id', $orgId);
        }
        
        return $builder->countAllResults();
    }
    
    /**
     * Get business locations assigned to activities in a workplan
     */
    public function getLocationsByWorkplan(int $workplanId)
    {
        return $this->select('activity_business_locations.*, business_locations.business_name, business_locations.gps_coordinates, activities.activity_name')
                   ->join('activities', 'activities.id = activity_business_locations.activity_id', 'left')
                   ->join('business_locations', 'business_locations.id = activity_business_locations.business_location_id', 'left')
                   ->where('activities.workplan_id', $workplanId)
                   ->where('activities.is_deleted', false)
                   ->where('business_locations.is_deleted', false)
                   ->orderBy('activities.activity_name', 'ASC')
                   ->orderBy('business_locations.business_name', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get location assignments by organization
     */
    public function getAssignmentsByOrg(int $orgId)
    {
        return $this->select('activity_business_locations.*, activities.activity_name, activities.activity_type, business_locations.business_name, business_locations.gps_coordinates')
                   ->join('activities', 'activities.id = activity_business_locations.activity_id', 'left')
                   ->join('business_locations', 'business_locations.id = activity_business_locations.business_location_id', 'left')
                   ->where('activity_business_locations.org_id', $orgId)
                   ->where('activities.is_deleted', false)
                   ->where('business_locations.is_deleted', false)
                   ->orderBy('activity_business_locations.assigned_at', 'DESC')
                   ->findAll();
    }
    
    /**
     * Get business location workload (count of active assignments)
     */
    public function getLocationWorkload(int $businessLocationId, int $orgId = null)
    {
        $builder = $this->select('activity_business_locations.*')
                       ->join('activities', 'activities.id = activity_business_locations.activity_id', 'left')
                       ->where('activity_business_locations.business_location_id', $businessLocationId)
                       ->where('activities.status', 'active')
                       ->where('activities.is_deleted', false);
        
        if ($orgId) {
            $builder->where('activity_business_locations.org_id', $orgId);
        }
        
        return $builder->countAllResults();
    }
    
    /**
     * Get locations with geographic details for activity
     */
    public function getLocationsWithGeoDetails(int $activityId)
    {
        return $this->select('activity_business_locations.*, business_locations.business_name, business_locations.gps_coordinates,
                             geo_countries.name as country_name, geo_provinces.name as province_name, geo_districts.name as district_name')
                   ->join('business_locations', 'business_locations.id = activity_business_locations.business_location_id', 'left')
                   ->join('geo_countries', 'geo_countries.id = business_locations.country_id', 'left')
                   ->join('geo_provinces', 'geo_provinces.id = business_locations.province_id', 'left')
                   ->join('geo_districts', 'geo_districts.id = business_locations.district_id', 'left')
                   ->where('activity_business_locations.activity_id', $activityId)
                   ->where('business_locations.is_deleted', false)
                   ->orderBy('geo_countries.name', 'ASC')
                   ->orderBy('geo_provinces.name', 'ASC')
                   ->orderBy('business_locations.business_name', 'ASC')
                   ->findAll();
    }
}
