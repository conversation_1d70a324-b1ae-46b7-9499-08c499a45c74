<?php

namespace App\Controllers;

use App\Models\BusinessEntityModel;
use CodeIgniter\Controller;

class BusinessEntities extends Controller
{
    protected $businessEntityModel;
    
    public function __construct()
    {
        $this->businessEntityModel = new BusinessEntityModel();
    }
    
    /**
     * Check admin authentication
     */
    private function checkAuth()
    {
        if (!session()->get('admin_logged_in')) {
            return redirect()->to('admin/login')->with('error', 'Please login to access this page.');
        }
        return null;
    }
    
    /**
     * Display list of business entities
     */
    public function index()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $entities = $this->businessEntityModel->getWithLocationsCount();
        
        $data = [
            'title' => 'Business Entities',
            'entities' => $entities
        ];
        
        return view('business_entities/business_entities_index', $data);
    }
    
    /**
     * Show create form
     */
    public function new()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $data = [
            'title' => 'Create New Business Entity'
        ];
        
        return view('business_entities/business_entities_create', $data);
    }
    
    /**
     * Handle create form submission
     */
    public function create()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $rules = [
            'business_name' => 'required|max_length[150]|is_unique[business_entities.business_name]',
            'remarks' => 'permit_empty'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $entityData = [
            'business_name' => $this->request->getPost('business_name'),
            'remarks' => $this->request->getPost('remarks'),
            'status' => 'active', // Automatically set to active
            'status_by' => session()->get('admin_user_id'),
            'status_at' => date('Y-m-d H:i:s'),
            'created_by' => session()->get('admin_user_id')
        ];
        
        if ($this->businessEntityModel->insert($entityData)) {
            return redirect()->to('admin/business-entities')->with('success', 'Business entity created successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create business entity.');
        }
    }
    
    /**
     * Show single business entity
     */
    public function show($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $entity = $this->businessEntityModel->getWithLocationsCount($id);
        
        if (!$entity) {
            return redirect()->to('admin/business-entities')->with('error', 'Business entity not found.');
        }
        
        $data = [
            'title' => 'Business Entity Details',
            'entity' => $entity
        ];
        
        return view('business_entities/business_entities_show', $data);
    }
    
    /**
     * Show edit form
     */
    public function edit($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $entity = $this->businessEntityModel->find($id);
        
        if (!$entity) {
            return redirect()->to('admin/business-entities')->with('error', 'Business entity not found.');
        }
        
        $data = [
            'title' => 'Edit Business Entity',
            'entity' => $entity
        ];
        
        return view('business_entities/business_entities_edit', $data);
    }
    
    /**
     * Handle update form submission
     */
    public function update($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $entity = $this->businessEntityModel->find($id);
        if (!$entity) {
            return redirect()->to('admin/business-entities')->with('error', 'Business entity not found.');
        }
        
        $rules = [
            'business_name' => "required|max_length[150]|is_unique[business_entities.business_name,id,{$id}]",
            'remarks' => 'permit_empty',
            'status' => 'required|in_list[active,inactive]'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $entityData = [
            'business_name' => $this->request->getPost('business_name'),
            'remarks' => $this->request->getPost('remarks'),
            'status' => $this->request->getPost('status'),
            'status_by' => session()->get('admin_user_id'),
            'status_at' => date('Y-m-d H:i:s'),
            'updated_by' => session()->get('admin_user_id')
        ];

        // Remove status_remarks if not provided to avoid database issues
        $statusRemarks = $this->request->getPost('status_remarks');
        if (!empty($statusRemarks)) {
            $entityData['status_remarks'] = $statusRemarks;
        }
        
        // Skip model validation since we already validated in controller
        $this->businessEntityModel->skipValidation(true);

        try {
            $result = $this->businessEntityModel->update($id, $entityData);
            if ($result) {
                return redirect()->to('admin/business-entities')->with('success', 'Business entity updated successfully.');
            } else {
                // Get model errors for debugging
                $errors = $this->businessEntityModel->errors();
                $errorMessage = !empty($errors) ? implode(', ', $errors) : 'Failed to update business entity.';
                log_message('error', 'Business Entity Update Failed: ' . $errorMessage . ' | Data: ' . json_encode($entityData));
                return redirect()->back()->withInput()->with('error', $errorMessage);
            }
        } catch (\Exception $e) {
            log_message('error', 'Business Entity Update Exception: ' . $e->getMessage() . ' | Data: ' . json_encode($entityData));
            return redirect()->back()->withInput()->with('error', 'Database error: ' . $e->getMessage());
        }
    }
    
    /**
     * Handle delete
     */
    public function delete($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $entity = $this->businessEntityModel->find($id);
        if (!$entity) {
            return redirect()->to('admin/business-entities')->with('error', 'Business entity not found.');
        }
        
        if ($this->businessEntityModel->softDelete($id)) {
            return redirect()->to('admin/business-entities')->with('success', 'Business entity deleted successfully.');
        } else {
            return redirect()->to('admin/business-entities')->with('error', 'Failed to delete business entity.');
        }
    }
}
