<?php

namespace App\Controllers;

use App\Models\BusinessLocationModel;

class BusinessEntitiesReport extends BaseController
{
    protected $businessLocationModel;
    
    public function __construct()
    {
        $this->businessLocationModel = new BusinessLocationModel();
    }
    
    /**
     * Check admin authentication
     */
    private function checkAuth()
    {
        if (!session()->get('admin_logged_in')) {
            return redirect()->to('admin/login')->with('error', 'Please login to access this page.');
        }
        return null;
    }
    
    /**
     * Display business entities report
     */
    public function index()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        // Get all business locations with complete details
        $reportData = $this->businessLocationModel->getReportData();
        
        $data = [
            'title' => 'Business Entities Report',
            'reportData' => $reportData
        ];
        
        return view('business_entities_report/business_entities_report_index', $data);
    }
}
