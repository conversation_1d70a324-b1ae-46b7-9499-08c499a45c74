<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-calendar-check me-2"></i>Workplan Details
                            </h2>
                            <p class="text-muted mb-0">View detailed information about this workplan</p>
                        </div>
                        <div>
                            <?= view('partials/back_button', [
                                'href' => base_url('admin/workplans'),
                                'label' => 'Back to Workplans List',
                                'class' => 'btn btn-secondary me-2'
                            ]) ?>
                            <a href="<?= base_url('admin/workplans/' . $workplan['id'] . '/edit') ?>" class="btn btn-warning">
                                <i class="bi bi-pencil me-2"></i>Edit Workplan
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Workplan Information -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>Workplan Information
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Title and Status Row -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label text-muted">Title</label>
                                <div class="text-dark fs-5">
                                    <i class="bi bi-bookmark me-2"></i><?= esc($workplan['title']) ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label text-muted">Status</label>
                                <div>
                                    <?php
                                    $statusClass = '';
                                    $statusIcon = '';
                                    switch ($workplan['status']) {
                                        case 'active':
                                            $statusClass = 'bg-success';
                                            $statusIcon = 'bi-check-circle';
                                            break;
                                        case 'inactive':
                                            $statusClass = 'bg-secondary';
                                            $statusIcon = 'bi-pause-circle';
                                            break;
                                        case 'completed':
                                            $statusClass = 'bg-primary';
                                            $statusIcon = 'bi-check-circle-fill';
                                            break;
                                        case 'cancelled':
                                            $statusClass = 'bg-danger';
                                            $statusIcon = 'bi-x-circle';
                                            break;
                                        default:
                                            $statusClass = 'bg-secondary';
                                            $statusIcon = 'bi-question-circle';
                                    }
                                    ?>
                                    <span class="badge <?= $statusClass ?> fs-6">
                                        <i class="<?= $statusIcon ?> me-1"></i><?= ucfirst($workplan['status']) ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Date Range Row -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Start Date</label>
                                <div class="text-dark">
                                    <i class="bi bi-calendar-event me-2"></i><?= date('F d, Y', strtotime($workplan['date_from'])) ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">End Date</label>
                                <div class="text-dark">
                                    <i class="bi bi-calendar-event me-2"></i><?= date('F d, Y', strtotime($workplan['date_to'])) ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Supervisor and Organization Row -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Supervisor</label>
                                <div class="text-dark">
                                    <i class="bi bi-person-badge me-2"></i><?= esc($workplan['supervisor_name']) ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Organization</label>
                                <div class="text-dark">
                                    <i class="bi bi-building me-2"></i><?= esc($workplan['org_name']) ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if (!empty($workplan['remarks'])): ?>
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Remarks</label>
                                    <div class="text-dark">
                                        <i class="bi bi-chat-text me-2"></i><?= nl2br(esc($workplan['remarks'])) ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($workplan['status_remarks'])): ?>
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label text-muted">Status Remarks</label>
                                    <div class="text-dark">
                                        <i class="bi bi-chat-square-text me-2"></i><?= nl2br(esc($workplan['status_remarks'])) ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Duration Info -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock me-2"></i>Duration
                    </h5>
                </div>
                <div class="card-body">
                    <?php
                    $startDate = new DateTime($workplan['date_from']);
                    $endDate = new DateTime($workplan['date_to']);
                    $interval = $startDate->diff($endDate);
                    $totalDays = $interval->days;
                    
                    $today = new DateTime();
                    $daysRemaining = 0;
                    $progress = 0;
                    
                    if ($today < $startDate) {
                        $daysRemaining = $today->diff($startDate)->days;
                        $status = 'Not Started';
                        $statusClass = 'text-warning';
                    } elseif ($today > $endDate) {
                        $status = 'Completed';
                        $statusClass = 'text-success';
                        $progress = 100;
                    } else {
                        $daysElapsed = $startDate->diff($today)->days;
                        $daysRemaining = $today->diff($endDate)->days;
                        $progress = ($daysElapsed / $totalDays) * 100;
                        $status = 'In Progress';
                        $statusClass = 'text-info';
                    }
                    ?>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span class="text-muted">Progress</span>
                            <span class="<?= $statusClass ?>"><?= $status ?></span>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-primary" style="width: <?= $progress ?>%"></div>
                        </div>
                        <small class="text-muted"><?= number_format($progress, 1) ?>% complete</small>
                    </div>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end border-secondary">
                                <h4 class="text-primary mb-0"><?= $totalDays ?></h4>
                                <small class="text-muted">Total Days</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning mb-0"><?= $daysRemaining ?></h4>
                            <small class="text-muted">Days <?= $today > $endDate ? 'Overdue' : 'Remaining' ?></small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Timestamps -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock-history me-2"></i>Timestamps
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Created</label>
                        <div class="text-dark">
                            <i class="bi bi-calendar-plus me-2"></i><?= date('F d, Y \a\t g:i A', strtotime($workplan['created_at'])) ?>
                        </div>
                    </div>
                    
                    <?php if ($workplan['updated_at'] !== $workplan['created_at']): ?>
                        <div class="mb-3">
                            <label class="form-label text-muted">Last Updated</label>
                            <div class="text-dark">
                                <i class="bi bi-calendar-check me-2"></i><?= date('F d, Y \a\t g:i A', strtotime($workplan['updated_at'])) ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($workplan['status_at'])): ?>
                        <div class="mb-3">
                            <label class="form-label text-muted">Status Changed</label>
                            <div class="text-dark">
                                <i class="bi bi-calendar-event me-2"></i><?= date('F d, Y \a\t g:i A', strtotime($workplan['status_at'])) ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Activities Management Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-activity me-2"></i>Activities Management
                    </h5>
                    <div>
                        <a href="<?= base_url('admin/activities?workplan_id=' . $workplan['id']) ?>" class="btn btn-outline-primary me-2">
                            <i class="bi bi-list me-1"></i>View All Activities
                        </a>
                        <a href="<?= base_url('admin/activities/new?workplan_id=' . $workplan['id']) ?>" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-1"></i>Add Activity
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Activities Statistics -->
                    <div class="row mb-4">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card bg-light border-0">
                                <div class="card-body text-center">
                                    <i class="bi bi-activity text-primary" style="font-size: 2rem;"></i>
                                    <h4 class="text-primary mt-2"><?= $activities_stats['total'] ?></h4>
                                    <p class="text-muted mb-0">Total Activities</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card bg-light border-0">
                                <div class="card-body text-center">
                                    <i class="bi bi-play-circle text-success" style="font-size: 2rem;"></i>
                                    <h4 class="text-success mt-2"><?= $activities_stats['active'] ?></h4>
                                    <p class="text-muted mb-0">Active</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card bg-light border-0">
                                <div class="card-body text-center">
                                    <i class="bi bi-upload text-info" style="font-size: 2rem;"></i>
                                    <h4 class="text-info mt-2"><?= $activities_stats['submitted'] ?></h4>
                                    <p class="text-muted mb-0">Submitted</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card bg-light border-0">
                                <div class="card-body text-center">
                                    <i class="bi bi-check-circle text-warning" style="font-size: 2rem;"></i>
                                    <h4 class="text-warning mt-2"><?= $activities_stats['approved'] ?></h4>
                                    <p class="text-muted mb-0">Approved</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activities -->
                    <?php if (!empty($recent_activities)): ?>
                        <div class="row">
                            <div class="col-12">
                                <h6 class="text-muted mb-3">
                                    <i class="bi bi-clock-history me-2"></i>Recent Activities
                                </h6>
                                <div class="list-group">
                                    <?php foreach ($recent_activities as $activity): ?>
                                        <div class="list-group-item d-flex justify-content-between align-items-start">
                                            <div class="ms-2 me-auto">
                                                <div class="fw-bold"><?= esc($activity['activity_name']) ?></div>
                                                <small class="text-muted">
                                                    <?= ucwords(str_replace('_', ' ', $activity['activity_type'])) ?> •
                                                    <?= date('M d, Y', strtotime($activity['date_from'])) ?> -
                                                    <?= date('M d, Y', strtotime($activity['date_to'])) ?>
                                                </small>
                                            </div>
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-<?=
                                                    $activity['status'] === 'active' ? 'success' :
                                                    ($activity['status'] === 'submitted' ? 'info' :
                                                    ($activity['status'] === 'approved' ? 'primary' : 'secondary'))
                                                ?> me-2">
                                                    <?= ucfirst($activity['status']) ?>
                                                </span>
                                                <a href="<?= base_url('admin/activities/' . $activity['id']) ?>" class="btn btn-outline-primary">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="bi bi-activity text-muted" style="font-size: 3rem;"></i>
                            <h6 class="text-muted mt-3">No Activities Found</h6>
                            <p class="text-muted">This workplan doesn't have any activities yet.</p>
                            <a href="<?= base_url('admin/activities/new?workplan_id=' . $workplan['id']) ?>" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>Create First Activity
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
