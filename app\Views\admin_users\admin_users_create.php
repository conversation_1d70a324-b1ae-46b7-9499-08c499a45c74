<?= $this->extend('templates/admin_template') ?>

<?= $this->section('styles') ?>
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-person-plus me-2"></i>Add New User
                            </h2>
                            <p class="text-muted mb-0">Create a new user account in the system</p>
                        </div>
                        <div>
                            <a href="<?= base_url('admin/users') ?>" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Validation Errors -->
    <?php if (session()->getFlashdata('errors')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <strong>Please correct the following errors:</strong>
            <ul class="mb-0 mt-2">
                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                    <li><?= esc($error) ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Error Messages -->
    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Create Form -->
    <div class="row">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-primary-custom mb-0">
                        <i class="bi bi-person-fill me-2"></i>User Information
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" action="<?= base_url('admin/users/create') ?>" enctype="multipart/form-data">
                        <?= csrf_field() ?>
                        
                        <div class="row">
                            <!-- Organization Display -->
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Organization</label>
                                <div class="form-control-plaintext bg-light p-2 rounded">
                                    <strong><?= esc($adminOrg['org_name']) ?></strong> (<?= esc($adminOrg['org_code']) ?>)
                                </div>
                                <small class="text-muted">Users will be created under your organization</small>
                            </div>

                            <!-- Name -->
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?= old('name') ?>" required maxlength="255">
                            </div>

                            <!-- Email -->
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?= old('email') ?>" required maxlength="500">
                            </div>

                            <!-- Role -->
                            <div class="col-md-6 mb-3">
                                <label for="role" class="form-label">Role <span class="text-danger">*</span></label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="">Select Role</option>
                                    <option value="user" <?= old('role') == 'user' ? 'selected' : '' ?>>User</option>
                                    <option value="guest" <?= old('role') == 'guest' ? 'selected' : '' ?>>Guest</option>
                                </select>
                            </div>

                            <!-- Position -->
                            <div class="col-md-6 mb-3">
                                <label for="position" class="form-label">Position</label>
                                <input type="text" class="form-control" id="position" name="position" 
                                       value="<?= old('position') ?>" maxlength="255">
                            </div>

                            <!-- Phone -->
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="text" class="form-control" id="phone" name="phone" 
                                       value="<?= old('phone') ?>" maxlength="200">
                            </div>
                        </div>

                        <!-- Permissions Section -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h6 class="text-primary-custom mb-3">
                                    <i class="bi bi-shield-check me-2"></i>Permissions
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="is_supervisor" name="is_supervisor" value="1"
                                                   <?= old('is_supervisor') ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="is_supervisor">
                                                <i class="bi bi-person-badge text-info me-1"></i>
                                                Supervisor
                                            </label>
                                            <div class="form-text">Supervisory access and team management</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="reports_to" class="form-label">
                                                <i class="bi bi-person-up text-warning me-1"></i>Reports To
                                            </label>
                                            <select class="form-select" id="reports_to" name="reports_to">
                                                <option value="">Select Supervisor (Optional)</option>
                                                <?php if (!empty($supervisors)): ?>
                                                    <?php foreach ($supervisors as $supervisor): ?>
                                                        <option value="<?= $supervisor['id'] ?>"
                                                                <?= old('reports_to') == $supervisor['id'] ? 'selected' : '' ?>>
                                                            <?= esc($supervisor['name']) ?> (<?= esc($supervisor['email']) ?>)
                                                        </option>
                                                    <?php endforeach; ?>
                                                <?php endif; ?>
                                            </select>
                                            <div class="form-text">Select the supervisor this user reports to</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- ID Photo Upload -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h6 class="text-primary-custom mb-3">
                                    <i class="bi bi-camera me-2"></i>Profile Photo (Optional)
                                </h6>
                                <div class="mb-3">
                                    <label for="id_photo" class="form-label">Upload Photo</label>
                                    <input type="file" class="form-control" id="id_photo" name="id_photo" 
                                           accept="image/*">
                                    <div class="form-text">
                                        Supported formats: JPG, PNG, GIF. Maximum size: 2MB
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="<?= base_url('admin/users') ?>" class="btn btn-secondary">
                                        <i class="bi bi-x-circle me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-2"></i>Create User
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Information Card -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card card-info">
                <div class="card-body">
                    <h6 class="text-info mb-3">
                        <i class="bi bi-info-circle me-2"></i>Important Information
                    </h6>
                    <ul class="mb-0">
                        <li>An activation email will be sent to the user's email address</li>
                        <li>The user will need to activate their account and set a password</li>
                        <li>Admin and Supervisor permissions can be modified later</li>
                        <li>All fields marked with <span class="text-danger">*</span> are required</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Preview uploaded image
document.getElementById('id_photo').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        // Validate file size (2MB)
        if (file.size > 2 * 1024 * 1024) {
            alert('File size must be less than 2MB');
            this.value = '';
            return;
        }
        
        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            alert('Please select a valid image file (JPG, PNG, or GIF)');
            this.value = '';
            return;
        }
    }
});

// Email validation
document.getElementById('email').addEventListener('blur', function() {
    const email = this.value;
    if (email) {
        // Basic email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            this.setCustomValidity('Please enter a valid email address');
        } else {
            this.setCustomValidity('');
        }
    }
});
</script>

<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
$(document).ready(function() {
    // Initialize Select2 for reports_to dropdown
    $('#reports_to').select2({
        theme: 'bootstrap-5',
        placeholder: 'Select Supervisor (Optional)',
        allowClear: true,
        width: '100%'
    });
});
</script>
<?= $this->endSection() ?>
