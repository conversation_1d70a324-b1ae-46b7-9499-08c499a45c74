<?= $this->extend('templates/field_template') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="card">
    <h1 class="card-title">👤 My Profile</h1>
    <p style="color: #666; margin-bottom: 0;">Manage your profile and settings</p>
</div>

<!-- Top Actions -->
<div class="card" style="margin-top: 10px;">
    <a href="<?= base_url('field/dashboard') ?>" class="btn btn-secondary btn-block">← Back to Dashboard</a>
</div>


<!-- Flash messages -->
<?php if (session()->getFlashdata('success')): ?>
    <div class="alert alert-success">
        <?= session()->getFlashdata('success') ?>
    </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
    <div class="alert alert-danger">
        <?= session()->getFlashdata('error') ?>
    </div>
<?php endif; ?>

<?php $errors = session()->getFlashdata('errors'); if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <ul style="margin:0; padding-left: 20px;">
            <?php foreach ($errors as $err): ?>
                <li><?= esc($err) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<!-- Profile Information -->
<div class="card">
    <h2 class="card-title">📋 Profile Information</h2>

    <form method="post" action="<?= base_url('field/profile/update') ?>">
        <?= csrf_field() ?>
        <!-- Name -->
        <div class="form-group">
            <label class="form-label" for="name">👤 Full Name</label>
            <input type="text"
                   id="name"
                   name="name"
                   class="form-control"
                   value="<?= esc($user['name']) ?>"
                   required>
        </div>

        <!-- Email -->
        <div class="form-group">
            <label class="form-label" for="email">📧 Email Address</label>
            <input type="email"
                   id="email"
                   name="email"
                   class="form-control"
                   value="<?= esc($user['email']) ?>"
                   required>
        </div>

        <!-- Phone -->
        <div class="form-group">
            <label class="form-label" for="phone">📱 Phone Number</label>
            <input type="tel"
                   id="phone"
                   name="phone"
                   class="form-control"
                   value="<?= esc($user['phone'] ?? '') ?>"
                   placeholder="+675 XXX XXXX">
        </div>

        <!-- Role (Read-only) -->
        <div class="form-group">
            <label class="form-label">🏷️ Role</label>
            <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; color: #666;">
                <?= esc($user['role']) ?> (Field Officer)
            </div>
        </div>


        <!-- Update Button -->
        <button type="submit" class="btn btn-success btn-block">
            💾 Update Profile
        </button>
    </form>
</div>

<!-- Change Password -->
<div class="card">
    <h2 class="card-title">🔒 Change Password</h2>

    <form id="passwordForm" method="post" action="<?= base_url('field/profile/change-password') ?>">
        <?= csrf_field() ?>
        <!-- Current Password -->
        <div class="form-group">
            <label class="form-label" for="current_password">🔑 Current Password</label>
            <input type="password"
                   id="current_password"
                   name="current_password"
                   class="form-control"
                   required>
        </div>

        <!-- New Password -->
        <div class="form-group">
            <label class="form-label" for="new_password">🆕 New Password</label>
            <input type="password"
                   id="new_password"
                   name="new_password"
                   class="form-control"
                   minlength="6"
                   required>
        </div>

        <!-- Confirm Password -->
        <div class="form-group">
            <label class="form-label" for="confirm_password">✅ Confirm New Password</label>
            <input type="password"
                   id="confirm_password"
                   name="confirm_password"
                   class="form-control"
                   minlength="6"
                   required>
        </div>

        <!-- Change Password Button -->
        <button type="submit" class="btn btn-warning btn-block">
            🔒 Change Password
        </button>
    </form>
</div>

<!-- App Settings -->
<div class="card">
    <h2 class="card-title">⚙️ App Settings</h2>

    <!-- Offline Mode -->
    <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px 0; border-bottom: 1px solid #eee;">
        <div>
            <strong>📱 Offline Mode</strong>
            <div style="font-size: 14px; color: #666;">Save data locally when offline</div>
        </div>
        <label style="position: relative; display: inline-block; width: 50px; height: 24px;">
            <input type="checkbox" id="offline_mode" checked style="opacity: 0; width: 0; height: 0;">
            <span style="position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #28a745; transition: .4s; border-radius: 24px;"></span>
            <span style="position: absolute; content: ''; height: 18px; width: 18px; left: 3px; bottom: 3px; background-color: white; transition: .4s; border-radius: 50%; transform: translateX(26px);"></span>
        </label>
    </div>

    <!-- Auto-sync -->
    <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px 0; border-bottom: 1px solid #eee;">
        <div>
            <strong>🔄 Auto-sync</strong>
            <div style="font-size: 14px; color: #666;">Automatically sync when online</div>
        </div>
        <label style="position: relative; display: inline-block; width: 50px; height: 24px;">
            <input type="checkbox" id="auto_sync" checked style="opacity: 0; width: 0; height: 0;">
            <span style="position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #28a745; transition: .4s; border-radius: 24px;"></span>
            <span style="position: absolute; content: ''; height: 18px; width: 18px; left: 3px; bottom: 3px; background-color: white; transition: .4s; border-radius: 50%; transform: translateX(26px);"></span>
        </label>
    </div>

    <!-- Photo Quality -->
    <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px 0; border-bottom: 1px solid #eee;">
        <div>
            <strong>📸 Photo Quality</strong>
            <div style="font-size: 14px; color: #666;">Lower quality saves bandwidth</div>
        </div>
        <select style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; background: white;">
            <option value="low">Low (Fast)</option>
            <option value="medium" selected>Medium</option>
            <option value="high">High (Slow)</option>
        </select>
    </div>

    <!-- Notifications -->
    <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px 0;">
        <div>
            <strong>🔔 Notifications</strong>
            <div style="font-size: 14px; color: #666;">Task reminders and updates</div>
        </div>
        <label style="position: relative; display: inline-block; width: 50px; height: 24px;">
            <input type="checkbox" id="notifications" checked style="opacity: 0; width: 0; height: 0;">
            <span style="position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #28a745; transition: .4s; border-radius: 24px;"></span>
            <span style="position: absolute; content: ''; height: 18px; width: 18px; left: 3px; bottom: 3px; background-color: white; transition: .4s; border-radius: 50%; transform: translateX(26px);"></span>
        </label>
    </div>
</div>

<!-- Storage Info -->
<div class="card">
    <h2 class="card-title">💾 Storage Information</h2>
    <div style="background: #f8f9fa; border-radius: 6px; padding: 15px;">
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
            <span>Local Data:</span>
            <strong id="si-local">Calculating…</strong>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
            <span>Cached Photos:</span>
            <strong id="si-cache">Calculating…</strong>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
            <span>Total Used:</span>
            <strong id="si-total">0</strong>
        </div>
        <div style="height: 6px; background: #e9ecef; border-radius: 3px; overflow: hidden;">
            <div id="si-usage-bar" style="background: linear-gradient(90deg, #28a745, #17a2b8); height: 100%; width: 0;"></div>
        </div>
        <div style="display: flex; justify-content: space-between; margin-top: 8px; font-size: 12px; color: #666;">
            <span>Storage Usage</span>
            <span id="si-usage-pct">0%</span>
        </div>
        <button class="btn btn-secondary btn-block" id="si-clear">
            🗑️ Clear Cache
        </button>
    </div>
</div>

<!-- App Information -->
<div class="card">
    <h2 class="card-title">ℹ️ App Information</h2>
    <div style="color: #666; font-size: 14px;">
        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
            <span>Version:</span>
            <strong>1.0.0</strong>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
            <span>Last Updated:</span>
            <strong><?= date('M j, Y') ?></strong>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
            <span>Server Status:</span>
            <strong style="color: #28a745;">Online</strong>
        </div>
        <div style="display: flex; justify-content: space-between;">
            <span>Last Sync:</span>
            <strong><?= date('H:i') ?></strong>
        </div>
    </div>
</div>


<style>
/* Toggle switch styles */
input[type="checkbox"]:checked + span {
    background-color: #28a745;
}

input[type="checkbox"]:checked + span + span {
    transform: translateX(26px);
}

input[type="checkbox"]:not(:checked) + span {
    background-color: #ccc;
}

input[type="checkbox"]:not(:checked) + span + span {
    transform: translateX(0);
}

/* Form validation styles */
.form-control:invalid {
    border-color: #dc3545;
}

.form-control:valid {
    border-color: #28a745;
}

/* Password strength indicator */
.password-strength {
    height: 4px;
    border-radius: 2px;
    margin-top: 5px;
    transition: all 0.3s;
}

.password-weak { background: #dc3545; width: 33%; }
.password-medium { background: #ffc107; width: 66%; }
// Storage information: real data from browser APIs
(async function storageInfo(){
  function formatBytes(bytes){
    if(bytes === 0 || bytes === undefined || bytes === null) return '0 B';
    const sizes=['B','KB','MB','GB','TB'];
    const i = Math.floor(Math.log(bytes)/Math.log(1024));
    return (bytes/Math.pow(1024,i)).toFixed(1)+' '+sizes[i];
  }

  // navigator.storage.estimate gives usage and quota
  try {
    if (navigator.storage && navigator.storage.estimate) {
      const est = await navigator.storage.estimate();
      const usage = est.usage || 0; // includes IndexedDB, Cache, localStorage, etc.
      const quota = est.quota || 0;
      const pct = quota ? Math.min(100, Math.round((usage/quota)*100)) : 0;
      const usageBar = document.getElementById('si-usage-bar');
      const usagePct = document.getElementById('si-usage-pct');
      if (usageBar) usageBar.style.width = pct+'%';
      if (usagePct) usagePct.textContent = pct+'%';
      const totalEl = document.getElementById('si-total');
      if (totalEl) totalEl.textContent = formatBytes(usage);
    }
  } catch(e) { console.warn('Storage estimate error', e); }

  // Approximate localStorage size
  try {
    let bytes = 0;
    for (let i=0;i<localStorage.length;i++){
      const k = localStorage.key(i);
      const v = localStorage.getItem(k);
      bytes += (k?.length||0) + (v?.length||0);
    }
    const el = document.getElementById('si-local');
    if (el) el.textContent = formatBytes(bytes);
  } catch(e) { /* ignore */ }

  // Cache Storage (approximate)
  try {
    if ('caches' in window) {
      let cacheBytes = 0;
      const names = await caches.keys();
      for (const name of names){
        const cache = await caches.open(name);
        const reqs = await cache.keys();
        for (const req of reqs){
          const res = await cache.match(req);
          if (res) {
            const buf = await res.clone().arrayBuffer().catch(()=>null);
            if (buf) cacheBytes += buf.byteLength;
          }
        }
      }
      const el = document.getElementById('si-cache');
      if (el) el.textContent = formatBytes(cacheBytes);
    }
  } catch(e) { console.warn('Cache read error', e); }

  // Last Sync (from localStorage if available)
  try {
    const lastSync = localStorage.getItem('field_last_sync_at');
    const el = document.getElementById('si-sync');
    if (el) el.textContent = lastSync ? new Date(parseInt(lastSync,10)).toLocaleString() : '—';
  } catch(e) { /* ignore */ }

  // Pending submissions (if you store queue in localStorage)
  try {
    const queued = JSON.parse(localStorage.getItem('field_pending_submissions')||'[]');
    const el = document.getElementById('si-pending');
    if (el) el.textContent = Array.isArray(queued) ? queued.length : 0;
  } catch(e) { /* ignore */ }

  // Clear cache handler
  const clearBtn = document.getElementById('si-clear');
  if (clearBtn) {
    clearBtn.addEventListener('click', async function(){
      if (!confirm('Clear browser cache and local storage?')) return;
      try {
        // Clear caches
        if ('caches' in window) {
          const names = await caches.keys();
          await Promise.all(names.map(n => caches.delete(n)));
        }
        // Clear localStorage app keys (avoid nuking everything, keep sessions if any server uses cookies)
        Object.keys(localStorage).forEach(k => {
          if (k.startsWith('field_')) localStorage.removeItem(k);
        });
        alert('Cache cleared. Data like offline settings may reset.');
        location.reload();
      } catch(e) {
        alert('Failed to clear cache.');
      }
    });
  }
})();

.password-strong { background: #28a745; width: 100%; }
</style>

<script>


// Password strength indicator
document.getElementById('new_password').addEventListener('input', function() {
    const password = this.value;
    let strength = 0;

    if (password.length >= 6) strength++;
    if (password.match(/[a-z]/) && password.match(/[A-Z]/)) strength++;
    if (password.match(/[0-9]/)) strength++;
    if (password.match(/[^a-zA-Z0-9]/)) strength++;

    // Remove existing strength indicator
    const existing = this.parentNode.querySelector('.password-strength');
    if (existing) existing.remove();

    if (password.length > 0) {
        const indicator = document.createElement('div');
        indicator.className = 'password-strength';

        if (strength <= 1) {
            indicator.classList.add('password-weak');
        } else if (strength <= 2) {
            indicator.classList.add('password-medium');
        } else {
            indicator.classList.add('password-strong');
        }

        this.parentNode.appendChild(indicator);
    }
});

// Settings toggle handlers
document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
        const setting = this.id;
        const enabled = this.checked;

        // Save setting to localStorage
        localStorage.setItem('field_setting_' + setting, enabled);

        // Show feedback
        const settingName = this.parentNode.previousElementSibling.querySelector('strong').textContent;
        console.log(`${settingName}: ${enabled ? 'Enabled' : 'Disabled'}`);
    });
});

// Load saved settings
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
        const setting = checkbox.id;
        const saved = localStorage.getItem('field_setting_' + setting);

        if (saved !== null) {
            checkbox.checked = saved === 'true';
        }
    });
});

function clearCache() {
    if (confirm('🗑️ Clear all cached data?\n\nThis will remove:\n• Cached photos\n• Offline data\n• App preferences\n\nYou may need to re-download some data.')) {
        // Simulate cache clearing
        const btn = event.target;
        const originalText = btn.textContent;

        btn.textContent = '🗑️ Clearing...';
        btn.disabled = true;

        setTimeout(() => {
            // Clear localStorage (except settings)
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (!key.startsWith('field_setting_')) {
                    localStorage.removeItem(key);
                }
            });

            alert('✅ Cache cleared successfully!\n\nFreed up 8.0 MB of storage space.');

            // Update storage display
            document.querySelector('.card:nth-last-child(3) strong:nth-of-type(1)').textContent = '0.1 MB';
            document.querySelector('.card:nth-last-child(3) strong:nth-of-type(2)').textContent = '0.0 MB';
            document.querySelector('.card:nth-last-child(3) strong:nth-of-type(3)').textContent = '0.1 MB';

            btn.textContent = originalText;
            btn.disabled = false;
        }, 2000);
    }
}
</script>

<?= $this->endSection() ?>
