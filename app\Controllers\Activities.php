<?php

namespace App\Controllers;

use App\Models\ActivityModel;
use App\Models\WorkplanModel;
use App\Models\UserModel;
use App\Models\DakoiiOrgModel;
use App\Models\ActivityUserModel;
use App\Models\ActivityBusinessLocationModel;
use CodeIgniter\Controller;

class Activities extends Controller
{
    protected $activityModel;
    protected $workplanModel;
    protected $userModel;
    protected $dakoiiOrgModel;
    protected $activityUserModel;
    protected $activityBusinessLocationModel;

    public function __construct()
    {
        $this->activityModel = new ActivityModel();
        $this->workplanModel = new WorkplanModel();
        $this->userModel = new UserModel();
        $this->dakoiiOrgModel = new DakoiiOrgModel();
        $this->activityUserModel = new ActivityUserModel();
        $this->activityBusinessLocationModel = new ActivityBusinessLocationModel();
        helper('form');
    }

    /**
     * Check if user is logged in
     */
    private function checkAuth()
    {
        if (!session()->get('admin_logged_in')) {
            return redirect()->to('admin')->with('error', 'Please login to access this page.');
        }
        return null;
    }
    
    /**
     * Display list of activities
     */
    public function index()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $orgId = session()->get('admin_org_id');

        // Check for workplan filter
        $workplanId = $this->request->getGet('workplan_id');
        $selectedWorkplan = null;

        $query = $this->activityModel->select('activities.*, workplans.title as workplan_title, users.name as status_by_name, supervisors.name as supervisor_name')
                                   ->join('workplans', 'workplans.id = activities.workplan_id', 'left')
                                   ->join('users', 'users.id = activities.status_by', 'left')
                                   ->join('users supervisors', 'supervisors.id = workplans.supervisor_id', 'left')
                                   ->where('activities.org_id', $orgId)
                                   ->where('activities.is_deleted', false);

        if ($workplanId) {
            $selectedWorkplan = $this->workplanModel->find($workplanId);
            if (!$selectedWorkplan || $selectedWorkplan['org_id'] != $orgId) {
                return redirect()->to('admin/activities')->with('error', 'Invalid workplan selected.');
            }
            $query->where('activities.workplan_id', $workplanId);
        }

        $activities = $query->orderBy('activities.date_from', 'DESC')->findAll();

        // Add assignment counts for each activity
        foreach ($activities as &$activity) {
            // Count assigned users
            $activity['assigned_users_count'] = $this->activityUserModel->where('activity_id', $activity['id'])->countAllResults();

            // Count assigned business locations
            $activity['assigned_businesses_count'] = $this->activityBusinessLocationModel->where('activity_id', $activity['id'])->countAllResults();
        }

        $data = [
            'title' => 'Activities Management',
            'activities' => $activities,
            'selected_workplan' => $selectedWorkplan,
            'workplan_id' => $workplanId
        ];

        return view('activities/activities_index', $data);
    }
    
    /**
     * Show create form
     */
    public function new()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $orgId = session()->get('admin_org_id');

        // Check for pre-selected workplan
        $workplanId = $this->request->getGet('workplan_id');
        $selectedWorkplan = null;

        if ($workplanId) {
            $selectedWorkplan = $this->workplanModel->find($workplanId);
            if (!$selectedWorkplan || $selectedWorkplan['org_id'] != $orgId) {
                return redirect()->to('admin/activities/new')->with('error', 'Invalid workplan selected.');
            }
        }

        // Get active workplans for dropdown
        $workplans = $this->workplanModel->where('org_id', $orgId)
                                        ->where('status', 'active')
                                        ->where('is_deleted', false)
                                        ->orderBy('title', 'ASC')
                                        ->findAll();

        $data = [
            'title' => 'Create New Activity',
            'workplans' => $workplans,
            'selected_workplan' => $selectedWorkplan,
            'workplan_id' => $workplanId,
            'activity_types' => [
                'price_collection' => 'Price Collection'
            ]
        ];

        return view('activities/activities_create', $data);
    }
    
    /**
     * Handle create form submission
     */
    public function create()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $rules = [
            'workplan_id' => 'required|integer',
            'activity_type' => 'required|max_length[50]',
            'activity_name' => 'required|max_length[200]',
            'date_from' => 'required|valid_date',
            'date_to' => 'required|valid_date',
            'remarks' => 'permit_empty'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        // Validate date range
        $dateFrom = $this->request->getPost('date_from');
        $dateTo = $this->request->getPost('date_to');
        
        if (strtotime($dateFrom) >= strtotime($dateTo)) {
            return redirect()->back()->withInput()->with('error', 'End date must be after start date.');
        }
        
        // Validate workplan belongs to organization
        $orgId = session()->get('admin_org_id');
        $workplan = $this->workplanModel->where('id', $this->request->getPost('workplan_id'))
                                      ->where('org_id', $orgId)
                                      ->where('is_deleted', false)
                                      ->first();
        
        if (!$workplan) {
            return redirect()->back()->withInput()->with('error', 'Invalid workplan selection.');
        }
        
        // Validate activity dates are within workplan period
        if (strtotime($dateFrom) < strtotime($workplan['date_from']) || 
            strtotime($dateTo) > strtotime($workplan['date_to'])) {
            return redirect()->back()->withInput()->with('error', 'Activity dates must be within the workplan period (' . 
                date('M d, Y', strtotime($workplan['date_from'])) . ' - ' . 
                date('M d, Y', strtotime($workplan['date_to'])) . ').');
        }
        
        $activityData = [
            'org_id' => $orgId,
            'workplan_id' => $this->request->getPost('workplan_id'),
            'activity_type' => $this->request->getPost('activity_type'),
            'activity_name' => $this->request->getPost('activity_name'),
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'remarks' => $this->request->getPost('remarks'),
            'status' => 'active', // Automatically set to active
            'status_by' => session()->get('admin_user_id'),
            'status_at' => date('Y-m-d H:i:s'),
            'created_by' => session()->get('admin_user_id')
        ];
        
        if ($this->activityModel->insert($activityData)) {
            return redirect()->to('admin/activities')->with('success', 'Activity created successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create activity.');
        }
    }
    
    /**
     * Show single activity
     */
    public function show($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $orgId = session()->get('admin_org_id');
        $activity = $this->activityModel->select('activities.*, workplans.title as workplan_title, 
                                                users.name as status_by_name, creator.name as created_by_name')
                                      ->join('workplans', 'workplans.id = activities.workplan_id', 'left')
                                      ->join('users', 'users.id = activities.status_by', 'left')
                                      ->join('users as creator', 'creator.id = activities.created_by', 'left')
                                      ->where('activities.id', $id)
                                      ->where('activities.org_id', $orgId)
                                      ->where('activities.is_deleted', false)
                                      ->first();
        
        if (!$activity) {
            return redirect()->to('admin/activities')->with('error', 'Activity not found.');
        }

        // Get assigned users
        $assignedUsers = $this->activityUserModel->getUsersByActivity($id);

        // Get assigned business locations with geographic details
        $assignedBusinesses = $this->activityBusinessLocationModel->getLocationsWithGeoDetails($id);

        // Get available users for assignment (users in the same organization)
        $availableUsers = $this->userModel->where('org_id', $orgId)
                                         ->where('deleted_at', null)
                                         ->where('status', 'active')
                                         ->orderBy('name', 'ASC')
                                         ->findAll();

        // Get available business locations for assignment
        $db = \Config\Database::connect();
        $availableBusinesses = $db->table('business_locations')
                                 ->select('business_locations.*, geo_countries.name as country_name, geo_provinces.name as province_name, geo_districts.name as district_name')
                                 ->join('geo_countries', 'geo_countries.id = business_locations.country_id', 'left')
                                 ->join('geo_provinces', 'geo_provinces.id = business_locations.province_id', 'left')
                                 ->join('geo_districts', 'geo_districts.id = business_locations.district_id', 'left')
                                 ->where('business_locations.is_deleted', false)
                                 ->where('business_locations.status', 'active')
                                 ->orderBy('business_locations.business_name', 'ASC')
                                 ->get()
                                 ->getResultArray();

        $data = [
            'title' => 'Activity Details',
            'activity' => $activity,
            'assigned_users' => $assignedUsers,
            'assigned_businesses' => $assignedBusinesses,
            'available_users' => $availableUsers,
            'available_businesses' => $availableBusinesses
        ];

        return view('activities/activities_show', $data);
    }

    /**
     * Assign user to activity
     */
    public function assignUser($activityId)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $orgId = session()->get('admin_org_id');

        // Verify activity exists and belongs to organization
        $activity = $this->activityModel->where('id', $activityId)
                                      ->where('org_id', $orgId)
                                      ->where('is_deleted', false)
                                      ->first();

        if (!$activity) {
            return redirect()->to('admin/activities')->with('error', 'Activity not found.');
        }

        $userId = $this->request->getPost('user_id');

        if (!$userId) {
            return redirect()->to('admin/activities/' . $activityId)->with('error', 'Please select a user to assign.');
        }

        // Verify user exists and belongs to organization
        $user = $this->userModel->where('id', $userId)
                               ->where('org_id', $orgId)
                               ->where('deleted_at', null)
                               ->first();

        if (!$user) {
            return redirect()->to('admin/activities/' . $activityId)->with('error', 'Invalid user selected.');
        }

        try {
            $this->activityUserModel->assignUserToActivity($orgId, $activityId, $userId);
            return redirect()->to('admin/activities/' . $activityId)->with('success', 'User assigned to activity successfully.');
        } catch (\Exception $e) {
            return redirect()->to('admin/activities/' . $activityId)->with('error', 'Failed to assign user: ' . $e->getMessage());
        }
    }

    /**
     * Remove user from activity
     */
    public function removeUser($activityId, $userId)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $orgId = session()->get('admin_org_id');

        // Verify activity exists and belongs to organization
        $activity = $this->activityModel->where('id', $activityId)
                                      ->where('org_id', $orgId)
                                      ->where('is_deleted', false)
                                      ->first();

        if (!$activity) {
            return redirect()->to('admin/activities')->with('error', 'Activity not found.');
        }

        try {
            $this->activityUserModel->removeUserFromActivity($activityId, $userId);
            return redirect()->to('admin/activities/' . $activityId)->with('success', 'User removed from activity successfully.');
        } catch (\Exception $e) {
            return redirect()->to('admin/activities/' . $activityId)->with('error', 'Failed to remove user: ' . $e->getMessage());
        }
    }

    /**
     * Assign business location to activity
     */
    public function assignBusiness($activityId)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $orgId = session()->get('admin_org_id');

        // Verify activity exists and belongs to organization
        $activity = $this->activityModel->where('id', $activityId)
                                      ->where('org_id', $orgId)
                                      ->where('is_deleted', false)
                                      ->first();

        if (!$activity) {
            return redirect()->to('admin/activities')->with('error', 'Activity not found.');
        }

        $businessLocationId = $this->request->getPost('business_location_id');

        if (!$businessLocationId) {
            return redirect()->to('admin/activities/' . $activityId)->with('error', 'Please select a business location to assign.');
        }

        // Verify business location exists
        $db = \Config\Database::connect();
        $businessLocation = $db->table('business_locations')
                              ->where('id', $businessLocationId)
                              ->where('is_deleted', false)
                              ->get()
                              ->getRowArray();

        if (!$businessLocation) {
            return redirect()->to('admin/activities/' . $activityId)->with('error', 'Invalid business location selected.');
        }

        try {
            $this->activityBusinessLocationModel->assignLocationToActivity($orgId, $activityId, $businessLocationId);
            return redirect()->to('admin/activities/' . $activityId)->with('success', 'Business location assigned to activity successfully.');
        } catch (\Exception $e) {
            return redirect()->to('admin/activities/' . $activityId)->with('error', 'Failed to assign business location: ' . $e->getMessage());
        }
    }

    /**
     * Remove business location from activity
     */
    public function removeBusiness($activityId, $businessLocationId)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;

        $orgId = session()->get('admin_org_id');

        // Verify activity exists and belongs to organization
        $activity = $this->activityModel->where('id', $activityId)
                                      ->where('org_id', $orgId)
                                      ->where('is_deleted', false)
                                      ->first();

        if (!$activity) {
            return redirect()->to('admin/activities')->with('error', 'Activity not found.');
        }

        try {
            $this->activityBusinessLocationModel->removeLocationFromActivity($activityId, $businessLocationId);
            return redirect()->to('admin/activities/' . $activityId)->with('success', 'Business location removed from activity successfully.');
        } catch (\Exception $e) {
            return redirect()->to('admin/activities/' . $activityId)->with('error', 'Failed to remove business location: ' . $e->getMessage());
        }
    }

    /**
     * Show edit form
     */
    public function edit($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $orgId = session()->get('admin_org_id');
        $activity = $this->activityModel->where('id', $id)
                                      ->where('org_id', $orgId)
                                      ->where('is_deleted', false)
                                      ->first();
        
        if (!$activity) {
            return redirect()->to('admin/activities')->with('error', 'Activity not found.');
        }
        
        // Get active workplans for dropdown
        $workplans = $this->workplanModel->where('org_id', $orgId)
                                        ->where('status', 'active')
                                        ->where('is_deleted', false)
                                        ->orderBy('title', 'ASC')
                                        ->findAll();
        
        $data = [
            'title' => 'Edit Activity',
            'activity' => $activity,
            'workplans' => $workplans,
            'activity_types' => [
                'price_collection' => 'Price Collection'
            ]
        ];
        
        return view('activities/activities_edit', $data);
    }
    
    /**
     * Handle update form submission
     */
    public function update($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $orgId = session()->get('admin_org_id');
        $activity = $this->activityModel->where('id', $id)
                                      ->where('org_id', $orgId)
                                      ->where('is_deleted', false)
                                      ->first();
        
        if (!$activity) {
            return redirect()->to('admin/activities')->with('error', 'Activity not found.');
        }
        
        $rules = [
            'workplan_id' => 'required|integer',
            'activity_type' => 'required|max_length[50]',
            'activity_name' => 'required|max_length[200]',
            'date_from' => 'required|valid_date',
            'date_to' => 'required|valid_date',
            'status' => 'required|in_list[active,submitted,approved,redo,cancelled]',
            'remarks' => 'permit_empty',
            'status_remarks' => 'permit_empty'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        // Validate date range
        $dateFrom = $this->request->getPost('date_from');
        $dateTo = $this->request->getPost('date_to');
        
        if (strtotime($dateFrom) >= strtotime($dateTo)) {
            return redirect()->back()->withInput()->with('error', 'End date must be after start date.');
        }
        
        // Validate workplan belongs to organization
        $workplan = $this->workplanModel->where('id', $this->request->getPost('workplan_id'))
                                      ->where('org_id', $orgId)
                                      ->where('is_deleted', false)
                                      ->first();
        
        if (!$workplan) {
            return redirect()->back()->withInput()->with('error', 'Invalid workplan selection.');
        }
        
        // Validate activity dates are within workplan period
        if (strtotime($dateFrom) < strtotime($workplan['date_from']) || 
            strtotime($dateTo) > strtotime($workplan['date_to'])) {
            return redirect()->back()->withInput()->with('error', 'Activity dates must be within the workplan period (' . 
                date('M d, Y', strtotime($workplan['date_from'])) . ' - ' . 
                date('M d, Y', strtotime($workplan['date_to'])) . ').');
        }
        
        $updateData = [
            'workplan_id' => $this->request->getPost('workplan_id'),
            'activity_type' => $this->request->getPost('activity_type'),
            'activity_name' => $this->request->getPost('activity_name'),
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'remarks' => $this->request->getPost('remarks'),
            'status' => $this->request->getPost('status'),
            'status_remarks' => $this->request->getPost('status_remarks'),
            'updated_by' => session()->get('admin_user_id')
        ];
        
        // Update status tracking if status changed
        if ($updateData['status'] !== $activity['status']) {
            $updateData['status_by'] = session()->get('admin_user_id');
            $updateData['status_at'] = date('Y-m-d H:i:s');
        }
        
        if ($this->activityModel->update($id, $updateData)) {
            return redirect()->to('admin/activities')->with('success', 'Activity updated successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update activity.');
        }
    }
    
    /**
     * Handle delete
     */
    public function delete($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $orgId = session()->get('admin_org_id');
        $activity = $this->activityModel->where('id', $id)
                                      ->where('org_id', $orgId)
                                      ->where('is_deleted', false)
                                      ->first();
        
        if (!$activity) {
            return redirect()->to('admin/activities')->with('error', 'Activity not found.');
        }
        
        $deleteData = [
            'is_deleted' => true,
            'deleted_by' => session()->get('admin_user_id'),
            'deleted_at' => date('Y-m-d H:i:s')
        ];
        
        if ($this->activityModel->update($id, $deleteData)) {
            return redirect()->to('admin/activities')->with('success', 'Activity deleted successfully.');
        } else {
            return redirect()->back()->with('error', 'Failed to delete activity.');
        }
    }
}
