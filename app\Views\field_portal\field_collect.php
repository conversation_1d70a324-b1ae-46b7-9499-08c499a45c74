<?= $this->extend('templates/field_template') ?>

<?= $this->section('content') ?>

<!-- <PERSON> Header -->
<div class="card">
    <h1 class="card-title">💰 Collect Prices</h1>
    <p style="color: #666; margin-bottom: 0;">Record price data for assigned items</p>
</div>

<!-- Current Task Info -->
<div class="card" style="background: #e8f4fd; border: 1px solid #bee5eb;">
    <h2 class="card-title" style="color: #0c5460;">📋 Current Task</h2>
    <div style="color: #0c5460; font-size: 14px;">
        <strong>Price Collection - Papindo Wewak</strong><br>
        📍 Wewak Town Market<br>
        📅 Due: <?= date('M j, Y', strtotime('+3 days')) ?><br>
        🛍️ Item 1 of 15
    </div>
</div>

<!-- Price Collection Form -->
<div class="card">
    <h2 class="card-title">Record Price Data</h2>
    
    <form id="priceForm" method="post" action="<?= base_url('field/api/submit-price') ?>">
        <!-- Item Information -->
        <div class="form-group">
            <label class="form-label">🛍️ Product Item</label>
            <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; margin-bottom: 10px;">
                <strong>Rice 1kg - Premium Brand</strong><br>
                <small style="color: #666;">Category: Staple Foods | Brand: SunRice</small>
            </div>
        </div>

        <!-- Store Information -->
        <div class="form-group">
            <label class="form-label">🏪 Store/Location</label>
            <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; margin-bottom: 10px;">
                <strong>Papindo Wewak</strong><br>
                <small style="color: #666;">Wewak Town Market, East Sepik Province</small>
            </div>
        </div>

        <!-- Price Input -->
        <div class="form-group">
            <label class="form-label" for="price">💵 Price (PGK)</label>
            <input type="number" 
                   id="price" 
                   name="price" 
                   class="form-control" 
                   step="0.01" 
                   min="0" 
                   placeholder="0.00" 
                   required>
        </div>

        <!-- Stock Status -->
        <div class="form-group">
            <label class="form-label">📦 Stock Status</label>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <label style="display: flex; align-items: center; gap: 5px; font-weight: normal;">
                    <input type="radio" name="stock_status" value="in_stock" checked>
                    ✅ In Stock
                </label>
                <label style="display: flex; align-items: center; gap: 5px; font-weight: normal;">
                    <input type="radio" name="stock_status" value="low_stock">
                    ⚠️ Low Stock
                </label>
                <label style="display: flex; align-items: center; gap: 5px; font-weight: normal;">
                    <input type="radio" name="stock_status" value="out_of_stock">
                    ❌ Out of Stock
                </label>
            </div>
        </div>

        <!-- Quality Notes -->
        <div class="form-group">
            <label class="form-label" for="notes">📝 Notes (Optional)</label>
            <textarea id="notes" 
                      name="notes" 
                      class="form-control" 
                      rows="3" 
                      placeholder="Any additional observations about quality, packaging, promotions, etc."></textarea>
        </div>

        <!-- Photo Upload (Simplified) -->
        <div class="form-group">
            <label class="form-label">📸 Photo (Optional)</label>
            <div style="text-align: center; padding: 20px; border: 2px dashed #ddd; border-radius: 6px;">
                <button type="button" class="btn btn-secondary" onclick="takePhoto()">
                    📷 Take Photo
                </button>
                <div style="font-size: 12px; color: #666; margin-top: 8px;">
                    Photo of product, price tag, or store
                </div>
            </div>
        </div>

        <!-- Hidden Fields -->
        <input type="hidden" name="task_id" value="1">
        <input type="hidden" name="goods_item" value="rice_1kg_premium">
        <input type="hidden" name="store_id" value="papindo_wewak">

        <!-- Submit Buttons -->
        <div style="display: flex; gap: 10px; margin-top: 30px;">
            <button type="submit" class="btn btn-success" style="flex: 1;">
                💾 Save & Next Item
            </button>
            <button type="button" class="btn btn-warning" onclick="saveDraft()" style="flex: 0 0 auto;">
                📄 Save Draft
            </button>
        </div>
    </form>
</div>

<!-- Progress Indicator -->
<div class="card">
    <h2 class="card-title">📊 Progress</h2>
    <div style="background: #f8f9fa; border-radius: 6px; padding: 15px;">
        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
            <span>Items Completed</span>
            <span><strong>0 of 15</strong></span>
        </div>
        <div style="background: #e9ecef; height: 8px; border-radius: 4px; overflow: hidden;">
            <div style="background: #28a745; height: 100%; width: 0%; transition: width 0.3s;"></div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col col-6">
        <a href="<?= base_url('field/tasks') ?>" class="btn btn-secondary btn-block">
            📋 Back to Tasks
        </a>
    </div>
    <div class="col col-6">
        <button class="btn btn-danger btn-block" onclick="pauseCollection()">
            ⏸️ Pause Collection
        </button>
    </div>
</div>

<style>
/* Form enhancements for mobile */
.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

input[type="radio"] {
    margin-right: 5px;
    transform: scale(1.2);
}

/* Photo upload area */
.photo-preview {
    max-width: 100%;
    height: auto;
    border-radius: 6px;
    margin-top: 10px;
}

/* Progress bar animation */
.progress-bar {
    transition: width 0.5s ease-in-out;
}

/* Loading state for form */
.form-loading {
    opacity: 0.6;
    pointer-events: none;
}

.form-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>

<script>
// Form submission handling
document.getElementById('priceForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const form = this;
    const formData = new FormData(form);
    
    // Add loading state
    form.classList.add('form-loading');
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = '💾 Saving...';
    submitBtn.disabled = true;
    
    // Simulate API call (replace with actual fetch)
    setTimeout(() => {
        // Success simulation
        alert('✅ Price data saved successfully!\n\nMoving to next item...');
        
        // Reset form for next item
        form.reset();
        form.classList.remove('form-loading');
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
        
        // Update progress (simulate)
        updateProgress(1, 15);
        
        // In real implementation, load next item data
        loadNextItem();
        
    }, 2000);
});

function updateProgress(completed, total) {
    const progressText = document.querySelector('.card:nth-last-child(2) strong');
    const progressBar = document.querySelector('.card:nth-last-child(2) div[style*="width"]');
    
    const percentage = (completed / total) * 100;
    
    progressText.textContent = `${completed} of ${total}`;
    progressBar.style.width = `${percentage}%`;
}

function loadNextItem() {
    // Simulate loading next item
    const items = [
        'Rice 1kg - Premium Brand',
        'Cooking Oil 1L - Sunflower',
        'Sugar 1kg - White Crystal',
        'Flour 1kg - All Purpose',
        'Salt 1kg - Iodized'
    ];
    
    const currentItem = Math.floor(Math.random() * items.length);
    const itemDisplay = document.querySelector('.form-group div[style*="background: #f8f9fa"]');
    itemDisplay.innerHTML = `<strong>${items[currentItem]}</strong><br><small style="color: #666;">Category: Staple Foods | Brand: Various</small>`;
}

function takePhoto() {
    // Simplified photo functionality
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        alert('📷 Camera functionality would be implemented here.\n\nIn a real app, this would:\n- Open camera\n- Capture photo\n- Compress for low bandwidth\n- Store locally until sync');
    } else {
        alert('📷 Camera not available on this device.\n\nYou can still submit the price data without a photo.');
    }
}

function saveDraft() {
    const form = document.getElementById('priceForm');
    const formData = new FormData(form);
    
    // Save to local storage for offline capability
    const draftData = {
        price: formData.get('price'),
        stock_status: formData.get('stock_status'),
        notes: formData.get('notes'),
        timestamp: new Date().toISOString()
    };
    
    localStorage.setItem('price_draft_' + formData.get('task_id'), JSON.stringify(draftData));
    
    alert('📄 Draft saved locally!\n\nYou can continue later or when you have better connectivity.');
}

function pauseCollection() {
    if (confirm('⏸️ Pause collection?\n\nYour progress will be saved and you can continue later.')) {
        // Save current progress
        saveDraft();
        
        // Redirect to tasks
        window.location.href = '<?= base_url('field/tasks') ?>';
    }
}

// Load draft data if available
document.addEventListener('DOMContentLoaded', function() {
    const taskId = document.querySelector('input[name="task_id"]').value;
    const draftKey = 'price_draft_' + taskId;
    const draftData = localStorage.getItem(draftKey);
    
    if (draftData) {
        const data = JSON.parse(draftData);
        
        if (confirm('📄 Found saved draft from ' + new Date(data.timestamp).toLocaleString() + '\n\nWould you like to continue where you left off?')) {
            document.getElementById('price').value = data.price || '';
            document.getElementById('notes').value = data.notes || '';
            
            if (data.stock_status) {
                document.querySelector(`input[name="stock_status"][value="${data.stock_status}"]`).checked = true;
            }
        }
    }
});
</script>

<?= $this->endSection() ?>
