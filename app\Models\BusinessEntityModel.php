<?php

namespace App\Models;

use CodeIgniter\Model;

class BusinessEntityModel extends Model
{
    protected $table = 'business_entities';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    
    protected $allowedFields = [
        'business_name',
        'remarks',
        'status',
        'status_by',
        'status_at',
        'status_remarks',
        'created_by',
        'updated_by'
    ];
    
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    
    protected $validationRules = [
        'business_name' => 'required|max_length[150]|is_unique[business_entities.business_name,id,{id}]',
        'status' => 'required|in_list[active,inactive]',
        'status_by' => 'permit_empty|integer',
        'created_by' => 'permit_empty|integer',
        'updated_by' => 'permit_empty|integer'
    ];
    
    protected $validationMessages = [
        'business_name' => [
            'required' => 'Business name is required.',
            'max_length' => 'Business name cannot exceed 150 characters.',
            'is_unique' => 'This business name already exists.'
        ],
        'status' => [
            'required' => 'Status is required.',
            'in_list' => 'Status must be either active or inactive.'
        ]
    ];
    
    protected $skipValidation = false;
    protected $cleanValidationRules = true;
    
    // Callbacks
    protected $beforeInsert = ['setCreatedBy'];
    protected $beforeUpdate = ['setUpdatedBy'];
    
    /**
     * Set created_by field before insert
     */
    protected function setCreatedBy(array $data)
    {
        if (!isset($data['data']['created_by']) && session()->has('admin_user_id')) {
            $data['data']['created_by'] = session()->get('admin_user_id');
        }
        return $data;
    }
    
    /**
     * Set updated_by field before update
     */
    protected function setUpdatedBy(array $data)
    {
        if (!isset($data['data']['updated_by']) && session()->has('admin_user_id')) {
            $data['data']['updated_by'] = session()->get('admin_user_id');
        }
        return $data;
    }
    
    /**
     * Get active business entities
     */
    public function getActive()
    {
        return $this->where('status', 'active')
                   ->where('is_deleted', false)
                   ->orderBy('business_name', 'ASC')
                   ->findAll();
    }
    
    /**
     * Get business entity with locations count
     */
    public function getWithLocationsCount($id = null)
    {
        $builder = $this->db->table($this->table . ' be')
                           ->select('be.*, COUNT(bl.id) as locations_count')
                           ->join('business_locations bl', 'be.id = bl.business_entity_id AND bl.is_deleted = 0', 'left')
                           ->where('be.is_deleted', false)
                           ->groupBy('be.id');

        if ($id !== null) {
            $builder->where('be.id', $id);
            return $builder->get()->getRowArray();
        }

        return $builder->orderBy('be.business_name', 'ASC')->get()->getResultArray();
    }
    
    /**
     * Update status with tracking
     */
    public function updateStatus($id, $status, $remarks = null)
    {
        $data = [
            'status' => $status,
            'status_by' => session()->get('admin_user_id'),
            'status_at' => date('Y-m-d H:i:s'),
            'status_remarks' => $remarks
        ];
        
        return $this->update($id, $data);
    }
    
    /**
     * Soft delete with tracking
     */
    public function softDelete($id)
    {
        $data = [
            'is_deleted' => true,
            'deleted_by' => session()->get('admin_user_id'),
            'deleted_at' => date('Y-m-d H:i:s')
        ];
        
        return $this->update($id, $data);
    }
}
