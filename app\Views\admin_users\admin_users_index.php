<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-people me-2"></i>Users Management
                            </h2>
                            <p class="text-muted mb-0">Manage system users and their permissions</p>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="<?= base_url('admin/dashboard') ?>" class="btn btn-secondary">
                                <i class="bi bi-house me-1"></i> Back to Dashboard
                            </a>
                            <a href="<?= base_url('admin/users/new') ?>" class="btn btn-primary">
                                <i class="bi bi-person-plus me-2"></i>Add New User
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i>
            <?= session()->getFlashdata('success') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <?= session()->getFlashdata('error') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('warning')): ?>
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <?= session()->getFlashdata('warning') ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Users Table -->
    <div class="row">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-primary-custom mb-0">
                        <i class="bi bi-table me-2"></i>All Users
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($users)): ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" style="background-color: #f8f9fa;">
                                <thead>
                                    <tr>
                                        <th>System No.</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Organization</th>
                                        <th>Role</th>
                                        <th>Status</th>
                                        <th>Admin</th>
                                        <th>Supervisor</th>
                                        <th>Reports To</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td>
                                                <span class="badge bg-info"><?= esc($user['sys_no']) ?></span>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if ($user['id_photo']): ?>
                                                        <img src="<?= base_url($user['id_photo']) ?>"
                                                             alt="<?= esc($user['name']) ?>"
                                                             class="rounded-circle me-2"
                                                             style="width: 32px; height: 32px; object-fit: cover;">
                                                    <?php else: ?>
                                                        <div class="bg-secondary rounded-circle me-2 d-flex align-items-center justify-content-center" 
                                                             style="width: 32px; height: 32px;">
                                                            <i class="bi bi-person text-white"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div>
                                                        <div class="fw-medium"><?= esc($user['name']) ?></div>
                                                        <?php if ($user['position']): ?>
                                                            <small class="text-muted"><?= esc($user['position']) ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <a href="mailto:<?= esc($user['email']) ?>" class="text-decoration-none">
                                                    <?= esc($user['email']) ?>
                                                </a>
                                            </td>
                                            <td>
                                                <?php if ($user['org_name']): ?>
                                                    <div>
                                                        <div class="fw-medium"><?= esc($user['org_name']) ?></div>
                                                        <small class="text-muted"><?= esc($user['org_code']) ?></small>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="text-muted">No Organization</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?= $user['role'] === 'user' ? 'primary' : 'secondary' ?>">
                                                    <?= ucfirst(esc($user['role'])) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php
                                                $statusClass = match($user['status']) {
                                                    'active' => 'success',
                                                    'pending' => 'warning',
                                                    'inactive' => 'danger',
                                                    default => 'secondary'
                                                };
                                                ?>
                                                <span class="badge bg-<?= $statusClass ?>">
                                                    <?= ucfirst(esc($user['status'])) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($user['is_admin']): ?>
                                                    <span class="badge bg-danger">
                                                        <i class="bi bi-shield-check me-1"></i>Admin
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($user['is_supervisor']): ?>
                                                    <span class="badge bg-info">
                                                        <i class="bi bi-person-badge me-1"></i>Supervisor
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (!empty($user['supervisor_name'])): ?>
                                                    <div class="d-flex align-items-center">
                                                        <i class="bi bi-person-up text-warning me-1"></i>
                                                        <span><?= esc($user['supervisor_name']) ?></span>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= date('M j, Y', strtotime($user['created_at'])) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('admin/users/' . $user['id']) ?>"
                                                       class="btn btn-sm btn-outline-info"
                                                       title="View Details">
                                                        <i class="bi bi-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('admin/users/' . $user['id'] . '/edit') ?>"
                                                       class="btn btn-sm btn-outline-warning"
                                                       title="Edit User">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="bi bi-people display-1 text-muted"></i>
                            <h4 class="text-muted mt-3">No Users Found</h4>
                            <p class="text-muted">Start by adding your first user to the system.</p>
                            <a href="<?= base_url('admin/users/new') ?>" class="btn btn-primary">
                                <i class="bi bi-person-plus me-2"></i>Add First User
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
