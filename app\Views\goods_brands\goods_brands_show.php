<?= $this->extend('templates/admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-eye me-2"></i>View Goods Brand
                            </h2>
                            <p class="text-muted mb-0">Detailed information about this goods brand</p>
                        </div>
                        <div>
                            <?= view('partials/back_button', [
                                'href' => base_url('admin/goods-brands'),
                                'label' => 'Back to Brands List',
                                'class' => 'btn btn-secondary me-2'
                            ]) ?>
                            <a href="<?= base_url('admin/goods-brands/' . $brand['id'] . '/edit') ?>" class="btn btn-warning">
                                <i class="bi bi-pencil me-2"></i>Edit
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Information -->
        <div class="col-lg-8">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-info-circle me-2"></i>Brand Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Brand Name</label>
                                <div class="text-dark h5"><?= esc($brand['brand_name']) ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Goods Group</label>
                                <div>
                                    <span class="badge bg-secondary fs-6">
                                        <i class="bi bi-collection me-1"></i><?= esc($brand['group_name']) ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Brand Type</label>
                                <div>
                                    <?php if ($brand['type'] === 'primary'): ?>
                                        <span class="badge bg-primary fs-6">
                                            <i class="bi bi-star me-1"></i>Primary Brand
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-warning text-dark fs-6">
                                            <i class="bi bi-arrow-repeat me-1"></i>Substitute Brand
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Status</label>
                                <div>
                                    <?php if ($brand['status'] === 'active'): ?>
                                        <span class="badge bg-success fs-6">
                                            <i class="bi bi-check-circle me-1"></i>Active
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary fs-6">
                                            <i class="bi bi-pause-circle me-1"></i>Inactive
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Type Information -->
                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6><i class="bi bi-info-circle me-2"></i>Brand Type Information:</h6>
                                <?php if ($brand['type'] === 'primary'): ?>
                                    <p class="mb-0">This is a <strong>Primary Brand</strong> - the main brand for the "<?= esc($brand['group_name']) ?>" goods group.</p>
                                <?php else: ?>
                                    <p class="mb-0">This is a <strong>Substitute Brand</strong> - an alternative brand that can replace the primary brand for the "<?= esc($brand['group_name']) ?>" goods group.</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Data Summary -->
            <div class="card card-dark mt-4">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-diagram-3 me-2"></i>Related Data
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="text-center p-3 border border-secondary rounded">
                                <i class="bi bi-box text-info" style="font-size: 2rem;"></i>
                                <h4 class="text-info mt-2"><?= $items_count ?></h4>
                                <p class="text-muted mb-0">Items</p>
                                <a href="<?= base_url('admin/goods-items?brand_id=' . $brand['id']) ?>"
                                   class="btn btn-outline-info mt-2">
                                    <i class="bi bi-eye me-1"></i>View Items
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Metadata Sidebar -->
        <div class="col-lg-4">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-clock-history me-2"></i>Record Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Created</label>
                        <div class="text-dark">
                            <?= date('M d, Y', strtotime($brand['created_at'])) ?><br>
                            <small class="text-muted"><?= date('H:i:s', strtotime($brand['created_at'])) ?></small>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label text-muted">Last Updated</label>
                        <div class="text-dark">
                            <?= date('M d, Y', strtotime($brand['updated_at'])) ?><br>
                            <small class="text-muted"><?= date('H:i:s', strtotime($brand['updated_at'])) ?></small>
                        </div>
                    </div>

                    <?php if (!empty($brand['status_at'])): ?>
                        <div class="mb-3">
                            <label class="form-label text-muted">Status Changed</label>
                            <div class="text-dark">
                                <?= date('M d, Y', strtotime($brand['status_at'])) ?><br>
                                <small class="text-muted"><?= date('H:i:s', strtotime($brand['status_at'])) ?></small>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Actions Card -->
            <div class="card card-dark mt-4">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-gear me-2"></i>Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?= base_url('admin/goods-brands/' . $brand['id'] . '/edit') ?>"
                           class="btn btn-warning">
                            <i class="bi bi-pencil me-2"></i>Edit Brand
                        </a>

                        <a href="<?= base_url('admin/goods-groups/' . $brand['goods_group_id']) ?>"
                           class="btn btn-secondary">
                            <i class="bi bi-collection me-2"></i>View Group
                        </a>

                        <a href="<?= base_url('admin/goods-items?brand_id=' . $brand['id']) ?>"
                           class="btn btn-info">
                            <i class="bi bi-box me-2"></i>View Items
                        </a>

                        <a href="<?= base_url('admin/goods-items/new?brand_id=' . $brand['id']) ?>"
                           class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>Add Item
                        </a>
                        
                        <?php if ($items_count == 0): ?>
                            <form method="post" action="<?= base_url('admin/goods-brands/' . $brand['id'] . '/delete') ?>"
                                  onsubmit="return confirm('Are you sure you want to delete this goods brand? This action cannot be undone.')">
                                <button type="submit" class="btn btn-danger w-100">
                                    <i class="bi bi-trash me-2"></i>Delete Brand
                                </button>
                            </form>
                        <?php else: ?>
                            <button type="button" class="btn btn-danger" disabled 
                                    title="Cannot delete - has associated items">
                                <i class="bi bi-trash me-2"></i>Delete Brand
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>
