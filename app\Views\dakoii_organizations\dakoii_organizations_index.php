<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-building me-2"></i>Organizations Management
                            </h2>
                            <p class="text-light mb-0">Manage client organizations and their information</p>
                        </div>
                        <div>
                            <a href="<?= base_url('dakoii/organizations/new') ?>" class="btn btn-primary">
                                <i class="bi bi-building-add me-2"></i>Add New Organization
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Organizations Table -->
    <div class="row">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-light mb-0">
                        <i class="bi bi-list me-2"></i>Organizations List
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($organizations)): ?>
                        <div class="text-center py-5">
                            <i class="bi bi-building-x text-muted" style="font-size: 4rem;"></i>
                            <h4 class="text-muted mt-3">No Organizations Found</h4>
                            <p class="text-muted">Start by adding your first organization.</p>
                            <a href="<?= base_url('dakoii/organizations/new') ?>" class="btn btn-primary">
                                <i class="bi bi-building-add me-2"></i>Add Organization
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-dark table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Organization</th>
                                        <th>Code</th>
                                        <th>Location</th>
                                        <th>Contact</th>
                                        <th>Status</th>
                                        <th>License</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($organizations as $org): ?>
                                        <tr>
                                            <td><?= esc($org['id']) ?></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                                        <i class="bi bi-building text-white"></i>
                                                    </div>
                                                    <div>
                                                        <strong><?= esc($org['org_name']) ?></strong>
                                                        <?php if ($org['description']): ?>
                                                            <br><small class="text-muted"><?= esc(substr($org['description'], 0, 50)) ?><?= strlen($org['description']) > 50 ? '...' : '' ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <code class="text-info"><?= esc($org['org_code']) ?></code>
                                            </td>
                                            <td>
                                                <?php if ($org['country_name'] || $org['province_name']): ?>
                                                    <small class="text-muted">
                                                        <?= $org['province_name'] ? esc($org['province_name']) . ', ' : '' ?>
                                                        <?= $org['country_name'] ? esc($org['country_name']) : '' ?>
                                                    </small>
                                                    <?php if ($org['is_locationlocked']): ?>
                                                        <br><span class="badge bg-warning text-dark">Location Locked</span>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <small class="text-muted">Not specified</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($org['email_addresses']): ?>
                                                    <small class="text-muted">
                                                        <i class="bi bi-envelope me-1"></i><?= esc($org['email_addresses']) ?>
                                                    </small>
                                                <?php endif; ?>
                                                <?php if ($org['phone_numbers']): ?>
                                                    <br><small class="text-muted">
                                                        <i class="bi bi-telephone me-1"></i><?= esc($org['phone_numbers']) ?>
                                                    </small>
                                                <?php endif; ?>
                                                <?php if (!$org['email_addresses'] && !$org['phone_numbers']): ?>
                                                    <small class="text-muted">No contact info</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($org['is_active']): ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">Inactive</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($org['license_status']): ?>
                                                    <?php
                                                    $licenseColors = [
                                                        'active' => 'success',
                                                        'expired' => 'danger',
                                                        'trial' => 'warning',
                                                        'suspended' => 'secondary'
                                                    ];
                                                    $color = $licenseColors[strtolower($org['license_status'])] ?? 'info';
                                                    ?>
                                                    <span class="badge bg-<?= $color ?>"><?= ucfirst($org['license_status']) ?></span>
                                                <?php else: ?>
                                                    <small class="text-muted">Not set</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('dakoii/organizations/' . $org['id']) ?>" class="btn btn-outline-info" title="View">
                                                        <i class="bi bi-eye"></i>
                                                    </a>
                                                    <a href="<?= base_url('dakoii/organizations/' . $org['id'] . '/edit') ?>" class="btn btn-outline-warning" title="Edit">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                    <form method="post" action="<?= base_url('dakoii/organizations/' . $org['id'] . '/delete') ?>" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this organization?')">
                                                        <?= csrf_field() ?>
                                                        <button type="submit" class="btn btn-outline-danger" title="Delete">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 0.875rem;
}
</style>
<?= $this->endSection() ?>
